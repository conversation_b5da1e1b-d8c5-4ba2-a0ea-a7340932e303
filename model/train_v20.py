"""
Refactored Training Script for Swin Transformer v8 Water Body Detection
Simplified, modular, and clean implementation

Features:
- No mixed precision training
- Clear separation of concerns
- Global step level loss recording
- Epoch level model saving and visualization
- Test/debug dataset modes
- Modular and maintainable code structure
"""

import argparse
import logging
import os
import sys
import time
import json
from pathlib import Path
from datetime import datetime

import torch
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP
from torch.utils.data import DataLoader, DistributedSampler
from torch.utils.tensorboard import SummaryWriter
import yaml
import numpy as np

# Configure logging
logs_dir = Path('logs')
logs_dir.mkdir(exist_ok=True)
log_filename = logs_dir / f"train_v20.log"

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[
        logging.FileHandler(log_filename, encoding='utf-8'),
        logging.StreamHandler()
    ],
    
    force=True
)
logger = logging.getLogger(__name__)

# Set specific loggers to appropriate levels
logging.getLogger('torch.distributed').setLevel(logging.WARNING)
logging.getLogger('torch.nn.parallel').setLevel(logging.WARNING)
logging.getLogger('PIL').setLevel(logging.WARNING)
logging.getLogger('data.dataset').setLevel(logging.WARNING)

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from data.dataset import WaterBodyDataset
from model.model_v20 import create_swin_water_net
from model.loss import Loss
from configs import get_config
from evaluation.visualization import save_visualization


class TrainingMetrics:
    """Simple class to track training metrics"""

    def __init__(self):
        # Only track best metrics for model saving, no step/epoch history
        self.best_metrics = {
            'accuracy_0_4_0_6': 0.0,
            'f1_score_0_4_0_6': 0.0,
            'iou_0_4_0_6': 0.0,
        }
        self.patience_counter = 0
    
    def update_best_metrics(self, val_metrics):
        """Check if current metrics are better and update if so"""
        current_metrics = {
            'accuracy_0_4_0_6': val_metrics.get('accuracy_0_4_0_6', 0),
            'f1_score_0_4_0_6': val_metrics.get('f1_score_0_4_0_6', 0),
            'iou_0_4_0_6': val_metrics.get('iou_0_4_0_6', 0)
        }
        
        # Check if ALL three metrics in 0.4-0.6 range have improved
        all_improved = all(
            current_metrics[metric] > self.best_metrics[metric] 
            for metric in ['accuracy_0_4_0_6', 'f1_score_0_4_0_6', 'iou_0_4_0_6']
        )
        
        if all_improved:
            self.best_metrics.update(current_metrics)
            self.patience_counter = 0
            return True
        else:
            self.patience_counter += 1
            return False


class DataManager:
    """Manages data loading and dataset creation"""
    
    def __init__(self, config, index_file, missing_db, local_rank, world_size, platform='L40'):
        self.config = config
        self.index_file = index_file
        self.missing_db = missing_db
        self.local_rank = local_rank
        self.world_size = world_size
        self.platform = platform

    def create_data_loaders(self):
        """Create train and validation data loaders"""
        # Get missing ratio filter parameters
        missing_ratio_config = getattr(self.config.data, 'missing_ratio_filter', {})
        max_missing_ratio = missing_ratio_config.get('max_missing_ratio', 1.0)

        # Create datasets with correct modes
        train_dataset = WaterBodyDataset(
            index_file=str(self.index_file) if self.index_file else "",
            missing_db_file=str(self.missing_db) if self.missing_db else None,
            config=self.config,
            expected_missing_ratio=max_missing_ratio,
            mode='train',  # Training uses test mode
            device=self.platform,
            use_missing_augmentation=True,
        )

        val_dataset = WaterBodyDataset(
            index_file=str(self.index_file) if self.index_file else "",
            missing_db_file=str(self.missing_db) if self.missing_db else None,
            config=self.config,
            expected_missing_ratio=max_missing_ratio,
            mode='val',  # Validation uses debug mode
            device=self.platform,
            use_missing_augmentation=True,
        )
        
        # Create samplers for distributed training
        train_sampler = DistributedSampler(
            train_dataset,
            num_replicas=self.world_size,
            rank=self.local_rank,
            shuffle=True,
            drop_last=True
        ) if self.world_size > 1 else None

        val_sampler = DistributedSampler(
            val_dataset,
            num_replicas=self.world_size,
            rank=self.local_rank,
            shuffle=False,
            drop_last=True
        ) if self.world_size > 1 else None
        
        # Create data loaders
        batch_size = self.config.training.batch_size
        
        train_loader = DataLoader(
            train_dataset,
            batch_size=batch_size,
            shuffle=(train_sampler is None),
            sampler=train_sampler,
            num_workers=self.config.training.num_workers,
            pin_memory=self.config.training.pin_memory,
            drop_last=True,
            persistent_workers=self.config.training.get('persistent_workers', True),
            prefetch_factor=self.config.training.get('prefetch_factor', None)
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=batch_size,
            shuffle=False,
            sampler=val_sampler,
            num_workers=self.config.inference.num_workers,
            pin_memory=True,
            drop_last=True
        )
        
        if self.local_rank == 0:
            logger.info(f"Train samples: {len(train_dataset)}")
            logger.info(f"Val samples: {len(val_dataset)}")
            logger.info(f"Per-GPU batch sizes - Train: {batch_size}, Val: {batch_size}")
        
        train_samples = train_dataset.samples 
        val_samples = val_dataset.samples

        return train_loader, val_loader, train_samples, val_samples


class ModelManager:
    """Manages model creation, optimization, and scheduling"""
    
    def __init__(self, config, device, local_rank, world_size):
        self.config = config
        self.device = device
        self.local_rank = local_rank
        self.world_size = world_size
        
        # Create model
        self.model = self._create_model()
        self.loss_fn = self._create_loss_function()
        self.optimizer = self._create_optimizer()
        self.scheduler = self._create_scheduler()
    
    def _create_model(self):
        """Create and setup the model"""
        model = create_swin_water_net(self.config)
        model = model.to(self.device)
        
        # Setup distributed training
        if self.world_size > 1:
            model = DDP(
                model,
                device_ids=[self.local_rank],
                output_device=self.local_rank,
                find_unused_parameters=False,
                broadcast_buffers=True,
                bucket_cap_mb=self.config.hardware.get('ddp_bucket_cap_mb', 25),
                static_graph=True
            )
        
        # Enable cudnn benchmarking for performance
        if self.config.performance.cudnn_benchmark:
            torch.backends.cudnn.benchmark = True
        
        # Report model parameters
        if self.local_rank == 0:
            trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
            total_params = sum(p.numel() for p in model.parameters())
            logger.info(f"Model: {trainable_params/1e6:.1f}M trainable / {total_params/1e6:.1f}M total parameters")
        
        return model
    
    def _create_loss_function(self):
        """Create loss function"""
        return Loss(
            bce_weight=self.config.training.loss_config.get('bce_weight', 0.3),
            dice_weight=self.config.training.loss_config.get('dice_weight', 0.4),
        ).to(self.device)
    
    def _create_optimizer(self):
        """Create optimizer"""
        learning_rate = float(self.config.training.learning_rate)
        weight_decay = float(self.config.training.weight_decay)
        betas = tuple(map(float, self.config.training.betas))
        eps = float(self.config.training.eps)
        
        return torch.optim.AdamW(
            self.model.parameters(),
            lr=learning_rate,
            betas=(betas[0], betas[1]) if len(betas) >= 2 else (0.9, 0.999),
            weight_decay=weight_decay,
            eps=eps
        )
    
    def _create_scheduler(self):
        """Create learning rate scheduler"""
        scheduler_config = self.config.training.lr_scheduler_params
        
        if self.config.training.scheduler == 'cosine_annealing_warm_restarts':
            return torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
                self.optimizer,
                T_0=int(scheduler_config.T_0),
                T_mult=int(scheduler_config.T_mult),
                eta_min=float(scheduler_config.eta_min)
            )
        else:
            return torch.optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer,
                T_max=int(self.config.training.total_epochs),
                eta_min=float(scheduler_config.eta_min)
            )


class SwinV8Trainer:
    """Main trainer class for Swin Transformer v8"""

    def __init__(self, config, local_rank=0, world_size=1, index_file=None, missing_db=None, platform='L40'):
        self.config = config
        self.local_rank = local_rank
        self.world_size = world_size
        self.device = torch.device(f'cuda:{local_rank}' if torch.cuda.is_available() else 'cpu')
        logger.info(f"The {self.device} is used.")
        self.platform = platform

        # Setup directories
        self._setup_directories()

        # Initialize components
        self.data_manager = DataManager(config, index_file, missing_db, local_rank, world_size, platform=self.platform)
        self.train_loader, self.val_loader, self.train_samples, self.val_samples = self.data_manager.create_data_loaders()

        self.model_manager = ModelManager(config, self.device, local_rank, world_size)
        self.metrics = TrainingMetrics()

        # Training state
        self.global_step = 0
        self.epoch = 0
        self.validate_every_global_steps = config.validation.get('validate_every_global_steps', 5000)
        self.early_stopping_patience = config.validation.get('early_stopping', {}).get('patience', 15)

        # Training loss accumulation for validation intervals
        self.accumulated_train_loss = 0.0
        self.steps_since_last_save = 0
        
        if local_rank == 0:
            self._save_val_indices()
            
        # Setup TensorBoard
        if local_rank == 0 and config.logging.get('use_tensorboard', True):
            self.writer = SummaryWriter(self.log_dir)

    def _setup_directories(self):
        """Setup logging and checkpoint directories"""
        experiment_name = self.config.logging.get('experiment_name',
                                                 f"swin_v8_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        res_dir = Path(self.config.logging.get('res_dir', 'Results'))

        self.log_dir = res_dir / 'logs' / experiment_name
        self.checkpoint_dir = res_dir / 'checkpoints' / experiment_name
        self.metrics_dir = res_dir / 'metrics' / experiment_name
        self.vis_dir = res_dir / 'visualizations' / experiment_name
        self.sample_dir = res_dir / 'samples' / experiment_name

        self.train_index_path = self.sample_dir / 'train_indices.json'
        self.val_index_path = self.sample_dir / "val_indices.json"
        
        if self.local_rank == 0:
            self.log_dir.mkdir(parents=True, exist_ok=True)
            self.checkpoint_dir.mkdir(parents=True, exist_ok=True)
            self.metrics_dir.mkdir(parents=True, exist_ok=True)
            self.vis_dir.mkdir(parents=True, exist_ok=True)
            self.sample_dir.mkdir(parents=True, exist_ok=True)
            
            # Save config
            with open(self.checkpoint_dir / 'config.yaml', 'w') as f:
                yaml.dump(self.config, f)

    def _save_val_indices(self):
        with open(self.train_index_path, 'w') as f:
            json.dump(self.train_samples, f, indent=2,
                    default=lambda o: str(type(o).__name__))
        logger.info(f"Saved train indices to {self.train_index_path}")

        with open(self.val_index_path, 'w') as f:
            json.dump(self.val_samples, f, indent=2,
                    default=lambda o: str(type(o).__name__))
        logger.info(f"Saved validation indices to {self.val_index_path}")

    def train_epoch(self):
        """Train for one epoch"""
        self.model_manager.model.train()

        # Set sampler epoch for distributed training
        if self.train_loader.sampler is not None and hasattr(self.train_loader.sampler, 'set_epoch'):
            self.train_loader.sampler.set_epoch(self.epoch)

        epoch_loss = 0.0
        num_batches = len(self.train_loader)
        log_interval = self.config.logging.get('log_interval', 50)

        # Save last batch for visualization
        last_batch = None
        last_outputs = None

        for batch_idx, batch in enumerate(self.train_loader):
            # Move batch to device
            processed_batch = {}
            for k, v in batch.items():
                if torch.is_tensor(v):
                    # Ensure proper dtype for different tensor types
                    if k in ['center_frame_idx', 'year', 'month']:
                        processed_batch[k] = v.to(self.device, dtype=torch.long, non_blocking=True)
                    else:
                        processed_batch[k] = v.to(self.device, non_blocking=True)
                else:
                    processed_batch[k] = v

            # Forward pass
            outputs = self.model_manager.model(processed_batch)
            loss = self.model_manager.loss_fn(outputs, processed_batch)

            # Backward pass
            loss.backward()

            # Optimizer step (with gradient accumulation)
            if (batch_idx + 1) % self.config.training.accumulation_steps == 0:
                if self.config.training.gradient_clipping > 0:
                    torch.nn.utils.clip_grad_norm_(
                        self.model_manager.model.parameters(),
                        self.config.training.gradient_clipping
                    )

                self.model_manager.optimizer.step()
                self.model_manager.optimizer.zero_grad()

            # Update metrics
            epoch_loss += loss.item()

            # Accumulate training loss for validation interval saving
            self.accumulated_train_loss += loss.item()
            self.steps_since_last_save += 1

            # Logging
            if self.local_rank == 0 and (batch_idx + 1) % log_interval == 0:
                progress = (batch_idx + 1) / num_batches * 100
                logger.info(f"Epoch {self.epoch} [{progress:5.1f}%] Loss={loss.item():.4f}")

            # Save last batch for visualization
            if batch_idx == len(self.train_loader) - 1:
                last_batch = {k: v.detach() if torch.is_tensor(v) else v for k, v in batch.items()}
                last_outputs = {k: {sk: sv.detach() if torch.is_tensor(sv) else sv for sk, sv in v.items()}
                                if isinstance(v, dict) else v.detach() if torch.is_tensor(v) else v
                                for k, v in outputs.items()}

            # Validate at specified intervals (global step level)
            if self.global_step % self.validate_every_global_steps == 0 and self.global_step > 0:
                self._perform_step_validation_and_save_training_loss()

            self.global_step += 1

            # Clear cache periodically
            if batch_idx % 200 == 0:
                torch.cuda.empty_cache()

        # Synchronize for distributed training
        if self.world_size > 1:
            dist.barrier()

        avg_epoch_loss = epoch_loss / num_batches
        return avg_epoch_loss, last_batch, last_outputs

    def _perform_step_validation_and_save_training_loss(self):
        """Perform validation and save both training and validation metrics at the same frequency"""
        val_loss, val_metrics = self.validate()

        # Calculate average training loss since last save (on all processes)
        avg_train_loss = self.accumulated_train_loss / max(1, self.steps_since_last_save)

        # Gather training loss from all processes for distributed training
        if self.world_size > 1:
            avg_train_loss = self._gather_metric(avg_train_loss)

        if self.local_rank == 0:
            # Record metrics to TensorBoard at validation frequency
            if hasattr(self, 'writer'):
                self.writer.add_scalar('train/loss', avg_train_loss, self.global_step)
                self.writer.add_scalar('val/loss', val_loss, self.global_step)
                for k, v in val_metrics.items():
                    self.writer.add_scalar(f'val/{k}', v, self.global_step)

                # Also record learning rate at validation frequency
                current_lr = self.model_manager.optimizer.param_groups[0]['lr']
                self.writer.add_scalar('lr/learning_rate', current_lr, self.global_step)

            # Save step-level training loss to JSON (at validation frequency)
            self._save_step_training_loss(avg_train_loss)

            # Save step-level validation metrics to JSON
            self._save_step_validation_metrics(val_loss, val_metrics)

            # Log results
            logger.info(f"Step {self.global_step}: Train Loss (avg)={avg_train_loss:.4f}, Val Loss={val_loss:.4f}")
            logger.info(f"Acc (0.4-0.6)={val_metrics.get('accuracy_0_4_0_6', float('nan')):.4f}, "
                       f"F1 (0.4-0.6)={val_metrics.get('f1_score_0_4_0_6', float('nan')):.4f}, "
                       f"IoU (0.4-0.6)={val_metrics.get('iou_0_4_0_6', float('nan')):.4f}")

        # Reset accumulation on all processes
        self.accumulated_train_loss = 0.0
        self.steps_since_last_save = 0

    def _save_step_validation_metrics(self, val_loss, val_metrics):
        """Save validation metrics at step level to JSON file"""
        validation_record = {
            'global_step': self.global_step,
            'epoch': self.epoch,
            'val_loss': val_loss,
            'val_metrics': val_metrics,
            'timestamp': datetime.now().isoformat()
        }

        # Append to validation history file
        val_history_file = self.metrics_dir / 'validation_history.json'

        # Load existing history or create new
        if val_history_file.exists():
            try:
                with open(val_history_file, 'r') as f:
                    history = json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                history = []
        else:
            history = []

        # Append new record
        history.append(validation_record)

        # Save updated history
        with open(val_history_file, 'w') as f:
            json.dump(history, f, indent=2)

    def _save_step_training_loss(self, avg_loss_value):
        """Save average training loss at validation frequency to JSON file"""
        training_record = {
            'global_step': self.global_step,
            'epoch': self.epoch,
            'train_loss_avg': avg_loss_value,
            'steps_averaged': self.validate_every_global_steps,
            'timestamp': datetime.now().isoformat()
        }

        # Append to training history file
        train_history_file = self.metrics_dir / 'training_history.json'

        # Load existing history or create new
        if train_history_file.exists():
            try:
                with open(train_history_file, 'r') as f:
                    history = json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                history = []
        else:
            history = []

        # Append new record
        history.append(training_record)

        # Save updated history
        with open(train_history_file, 'w') as f:
            json.dump(history, f, indent=2)

    @torch.no_grad()
    def validate(self):
        """Validate the model"""
        self.model_manager.model.eval()
        val_loss = 0.0
        total_metrics = {}
        num_valid_batches = {}
        n_batches = 0

        # Synchronization for distributed training
        if self.world_size > 1:
            dist.barrier()

        for batch in self.val_loader:
            # Move batch to device
            processed_batch = {}
            for k, v in batch.items():
                if torch.is_tensor(v):
                    # Ensure proper dtype for different tensor types
                    if k in ['center_frame_idx', 'year', 'month']:
                        processed_batch[k] = v.to(self.device, dtype=torch.long, non_blocking=True)
                    else:
                        processed_batch[k] = v.to(self.device, non_blocking=True)
                else:
                    processed_batch[k] = v

            outputs = self.model_manager.model(processed_batch)
            loss, metrics = self.model_manager.loss_fn(outputs, processed_batch, 'eval')

            val_loss += loss.item()
            n_batches += 1

            # Aggregate metrics across batches
            for range_key, value in metrics.items():
                if not np.isnan(value):
                    total_metrics[range_key] = total_metrics.get(range_key, 0.0) + value
                    num_valid_batches[range_key] = num_valid_batches.get(range_key, 0) + 1

        # Synchronization for distributed training
        if self.world_size > 1:
            dist.barrier()

        # Average metrics
        avg_loss = val_loss / max(1, n_batches)
        avg_metrics = {key: total_metrics[key] / max(1, num_valid_batches.get(key, 0))
                      for key in total_metrics}

        # Gather metrics from all processes
        if self.world_size > 1:
            avg_loss = self._gather_metric(avg_loss)
            avg_metrics = {k: self._gather_metric(v) for k, v in avg_metrics.items()}

        return avg_loss, avg_metrics

    def _gather_metric(self, metric):
        """Gather metric from all processes"""
        if self.world_size == 1:
            return metric

        metric_tensor = torch.tensor(metric).to(self.device)
        dist.all_reduce(metric_tensor, op=dist.ReduceOp.SUM)
        return metric_tensor.item() / self.world_size

    def save_checkpoint(self, is_best=False):
        """Save model checkpoint"""
        if self.local_rank != 0:
            return

        # Get model state dict
        model = self.model_manager.model.module if hasattr(self.model_manager.model, 'module') else self.model_manager.model

        checkpoint = {
            'epoch': self.epoch,
            'global_step': self.global_step,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': self.model_manager.optimizer.state_dict(),
            'scheduler_state_dict': self.model_manager.scheduler.state_dict(),
            'best_metrics': self.metrics.best_metrics,
            'config': self.config,
            'patience_counter': self.metrics.patience_counter,
            'timestamp': datetime.now().isoformat(),
        }

        # Save last checkpoint
        last_path = self.checkpoint_dir / 'last.pt'
        torch.save(checkpoint, last_path)
        logger.info(f"Saved latest checkpoint: {last_path}")

        # Save best checkpoint
        if is_best:
            best_path = self.checkpoint_dir / 'best.pt'
            torch.save(checkpoint, best_path)
            logger.info(f"Saved best checkpoint: {best_path}")

        # Save periodic checkpoint
        if self.epoch % 5 == 0:
            periodic_path = self.checkpoint_dir / f'epoch_{self.epoch}.pt'
            torch.save(checkpoint, periodic_path)
            logger.info(f"Saved periodic checkpoint: {periodic_path}")

    def save_metrics_summary(self):
        """Save metrics summary (only best metrics, no step history)"""
        if self.local_rank != 0:
            return

        # Save only the best metrics summary
        summary = {
            'best_metrics': self.metrics.best_metrics,
            'patience_counter': self.metrics.patience_counter,
            'current_epoch': self.epoch,
            'current_global_step': self.global_step,
            'timestamp': datetime.now().isoformat()
        }

        with open(self.metrics_dir / 'metrics_summary.json', 'w') as f:
            json.dump(summary, f, indent=2)

    def train(self):
        """Main training loop"""
        if self.local_rank == 0:
            logger.info(f"SETUP | Total Epochs: {self.config.training.total_epochs} | "
                        f"GPUs: {self.world_size} | Batch Size per GPU: {self.config.training.batch_size}")

        vis_interval = self.config.visualization.get('vis_interval', 5)
        vis_config = self.config.visualization.copy()
        vis_config['vis_dir'] = self.vis_dir

        start_time = time.time()

        for epoch in range(self.config.training.total_epochs):
            self.epoch = epoch
            epoch_start_time = time.time()

            # Train one epoch
            avg_epoch_loss, last_batch, last_outputs = self.train_epoch()

            # Validate at epoch level for model saving only
            val_loss, val_metrics = self.validate()

            # Epoch-level operations (no TensorBoard logging, only for model management)
            if self.local_rank == 0:

                # Calculate timing
                epoch_time = time.time() - epoch_start_time
                total_time = time.time() - start_time
                avg_epoch_time = total_time / (epoch + 1)
                remaining_epochs = self.config.training.total_epochs - epoch - 1
                eta = remaining_epochs * avg_epoch_time

                # Log epoch summary (for monitoring only, not saved)
                logger.info(f"EPOCH {epoch:3d} | Train Loss: {avg_epoch_loss:.4f} | Val Loss: {val_loss:.4f}")
                logger.info(f"Time: {epoch_time/3600:.1f}h | ETA: {eta/3600:.1f}h")
                logger.info(f"Val Metrics (0-1): Acc={val_metrics.get('accuracy_0_1', 0):.4f}, "
                            f"F1={val_metrics.get('f1_score_0_1', 0):.4f}, "
                            f"IoU={val_metrics.get('iou_0_1', 0):.4f}")
                logger.info(f"Val Metrics (0.2-0.8): Acc={val_metrics.get('accuracy_0_2_0_8', 0):.4f}, "
                            f"F1={val_metrics.get('f1_score_0_2_0_8', 0):.4f}, "
                            f"IoU={val_metrics.get('iou_0_2_0_8', 0):.4f}")
                logger.info(f"Val Metrics (0.4-0.6): Acc={val_metrics.get('accuracy_0_4_0_6', 0):.4f}, "
                            f"F1={val_metrics.get('f1_score_0_4_0_6', 0):.4f}, "
                            f"IoU={val_metrics.get('iou_0_4_0_6', 0):.4f}")

            # Check for best model
            is_best = self.metrics.update_best_metrics(val_metrics)
            if is_best and self.local_rank == 0:
                logger.info(f"New best model! Acc={self.metrics.best_metrics['accuracy_0_4_0_6']:.4f}, "
                            f"F1={self.metrics.best_metrics['f1_score_0_4_0_6']:.4f}, "
                            f"IoU={self.metrics.best_metrics['iou_0_4_0_6']:.4f}")

            # Save checkpoints
            self.save_checkpoint(is_best)
            if self.local_rank == 0:
                self.save_metrics_summary()

            # Save visualization
            if (self.local_rank == 0 and last_batch is not None and
                last_outputs is not None and epoch % vis_interval == 0):
                save_visualization(last_batch, last_outputs, epoch, vis_config, logger)
                logger.info(f"Visualization saved for epoch {epoch}")

            # Update learning rate
            self.model_manager.scheduler.step()

            # Check for early stopping
            if (self.early_stopping_patience > 0 and
                self.metrics.patience_counter >= self.early_stopping_patience):
                if self.local_rank == 0:
                    logger.info(f"Early stopping triggered after {self.early_stopping_patience} epochs")
                break

        # Training completed
        if self.local_rank == 0:
            logger.info(f"Training completed! Best metrics: "
                        f"Acc={self.metrics.best_metrics['accuracy_0_4_0_6']:.4f}, "
                        f"F1={self.metrics.best_metrics['f1_score_0_4_0_6']:.4f}, "
                        f"IoU={self.metrics.best_metrics['iou_0_4_0_6']:.4f}")

            # Save final summary
            final_summary = {
                'training_completed': True,
                'total_epochs': self.epoch + 1,
                'best_metrics': self.metrics.best_metrics,
                'total_training_steps': self.global_step,
                'final_patience_counter': self.metrics.patience_counter
            }

            with open(self.metrics_dir / 'final_summary.json', 'w') as f:
                json.dump(final_summary, f, indent=2)
            logger.info("Training summary saved")


def setup_distributed():
    """Initialize distributed training"""
    if 'LOCAL_RANK' in os.environ:
        local_rank = int(os.environ['LOCAL_RANK'])
        world_size = int(os.environ['WORLD_SIZE'])

        # Configure NCCL for stability
        os.environ.setdefault('NCCL_DEBUG', 'WARN')
        os.environ.setdefault('NCCL_IB_DISABLE', '1')
        os.environ.setdefault('NCCL_P2P_DISABLE', '1')
        os.environ.setdefault('NCCL_TIMEOUT', '1800')

        torch.cuda.set_device(local_rank)
        dist.init_process_group(backend='nccl')

        return local_rank, world_size
    else:
        return 0, 1


def main():
    """Main training function"""
    parser = argparse.ArgumentParser(description='Swin Transformer v8 Training (Refactored)')
    parser.add_argument('--config', type=str, required=True, help='Path to configuration file')
    parser.add_argument('--index_file', type=str, required=True, help='Path to data index file')
    parser.add_argument('--missing_db', type=str, required=True, help='Path to missing data database')
    parser.add_argument('--resume', type=str, default=None, help='Path to checkpoint to resume from')
    parser.add_argument('--platform', type=str, default='L40', help='Platform for training')
    
    args = parser.parse_args()

    # Setup distributed training
    local_rank, world_size = setup_distributed()

    # Load configuration
    config = get_config(args.config)

    # Set random seed
    torch.manual_seed(config.seed)
    np.random.seed(config.seed)

    # Create trainer
    trainer = SwinV8Trainer(config, local_rank, world_size, args.index_file, args.missing_db, args.platform)

    # Resume from checkpoint if specified
    if args.resume:
        if local_rank == 0:
            logger.info(f"Loading checkpoint from {args.resume}")

        checkpoint = torch.load(args.resume, map_location='cpu', weights_only=False)

        # Load model weights
        target_model = (trainer.model_manager.model.module
                       if hasattr(trainer.model_manager.model, 'module')
                       else trainer.model_manager.model)

        missing_keys, unexpected_keys = target_model.load_state_dict(
            checkpoint['model_state_dict'], strict=False
        )

        if local_rank == 0:
            if missing_keys:
                logger.warning(f"Missing keys: {len(missing_keys)} keys")
            if unexpected_keys:
                logger.warning(f"Unexpected keys: {len(unexpected_keys)} keys")

        # Load optimizer and scheduler states
        try:
            trainer.model_manager.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            trainer.model_manager.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        except KeyError as e:
            if local_rank == 0:
                logger.warning(f"Optimizer/Scheduler state not found: {e}")

        # Load training progress
        trainer.epoch = checkpoint.get('epoch', 0)
        trainer.global_step = checkpoint.get('global_step', 0)

        # Load metrics (only best metrics, no history)
        if 'best_metrics' in checkpoint:
            trainer.metrics.best_metrics = checkpoint['best_metrics']
        if 'patience_counter' in checkpoint:
            trainer.metrics.patience_counter = checkpoint['patience_counter']

        if local_rank == 0:
            logger.info(f"Checkpoint loaded. Resuming from epoch {trainer.epoch}, step {trainer.global_step}")

    # Start training
    trainer.train()

    # Cleanup
    if world_size > 1:
        dist.destroy_process_group()


if __name__ == "__main__":
    main()
